const { Em<PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('../../../config/config.js');

module.exports = {
    name: 'giveaway',
    category: 'info',
    aliases: ['gstart', 'startgiveaway'],
    cooldown: 60,
    usage: '<prize> <duration>',
    description: 'Start a giveaway',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: true,
    ServerOwnerOnly: true,
    DevloperTeamOnly: false,
    async execute(message, args) {
        const prize = args.join(' ') || 'Amazing Prize';
        const giveawayId = `giveaway_${Date.now()}`;
        const giveawayFile = path.join(__dirname, '../../../data/giveaways.json');

        // Ensure data directory exists
        const dataDir = path.dirname(giveawayFile);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        // Initialize giveaway data
        let giveaways = {};
        if (fs.existsSync(giveawayFile)) {
            try {
                giveaways = JSON.parse(fs.readFileSync(giveawayFile, 'utf8'));
            } catch (error) {
                giveaways = {};
            }
        }

        giveaways[giveawayId] = {
            prize: prize,
            host: message.author.id,
            channelId: message.channel.id,
            messageId: null,
            entries: [],
            active: true,
            startTime: Date.now()
        };

        // Create embed
        const embed = new EmbedBuilder()
            .setTitle('🎉 RankBreaker\'s Giveaway 🎉')
            .setDescription(`**Prize:** ${prize}\n\n**How to Enter:**\nClick the 🎁 button below to enter!\n\n**Entries:** 0\n\n**Status:** Active`)
            .setColor('#3498db')
            .setFooter({ text: `Giveaway ID: ${giveawayId}`, iconURL: message.client.user.displayAvatarURL() })
            .setTimestamp()
            .setThumbnail('https://cdn.discordapp.com/emojis/742043142750388304.png');

        // Create button
        const button = new ButtonBuilder()
            .setCustomId(`giveaway_enter_${giveawayId}`)
            .setLabel('🎁 Enter Giveaway')
            .setStyle(ButtonStyle.Primary);

        const row = new ActionRowBuilder().addComponents(button);

        // Send message
        const giveawayMessage = await message.channel.send({ 
            embeds: [embed], 
            components: [row] 
        });

        // Update giveaway data with message ID
        giveaways[giveawayId].messageId = giveawayMessage.id;
        fs.writeFileSync(giveawayFile, JSON.stringify(giveaways, null, 2));

        // Delete command message
        if (message.deletable) {
            message.delete().catch(() => {});
        }

        // Button interaction handler
        const filter = (interaction) => interaction.customId.startsWith('giveaway_enter_');
        const collector = giveawayMessage.createMessageComponentCollector({ filter });

        collector.on('collect', async (interaction) => {
            const currentGiveawayId = interaction.customId.split('_')[2];
            
            // Read current giveaway data
            let currentGiveaways = {};
            if (fs.existsSync(giveawayFile)) {
                try {
                    currentGiveaways = JSON.parse(fs.readFileSync(giveawayFile, 'utf8'));
                } catch (error) {
                    currentGiveaways = {};
                }
            }

            if (!currentGiveaways[currentGiveawayId] || !currentGiveaways[currentGiveawayId].active) {
                return interaction.reply({ 
                    content: '❌ This giveaway is no longer active!', 
                    ephemeral: true 
                });
            }

            const userId = interaction.user.id;
            
            // Check if user already entered
            if (currentGiveaways[currentGiveawayId].entries.includes(userId)) {
                return interaction.reply({ 
                    content: '⚠️ You have already entered this giveaway!', 
                    ephemeral: true 
                });
            }

            // Add user to entries
            currentGiveaways[currentGiveawayId].entries.push(userId);
            fs.writeFileSync(giveawayFile, JSON.stringify(currentGiveaways, null, 2));

            // Update embed
            const updatedEmbed = new EmbedBuilder()
                .setTitle('🎉 RankBreaker\'s Giveaway 🎉')
                .setDescription(`**Prize:** ${currentGiveaways[currentGiveawayId].prize}\n\n**How to Enter:**\nClick the 🎁 button below to enter!\n\n**Entries:** ${currentGiveaways[currentGiveawayId].entries.length}\n\n**Status:** Active`)
                .setColor('#3498db')
                .setFooter({ text: `Giveaway ID: ${currentGiveawayId}`, iconURL: interaction.client.user.displayAvatarURL() })
                .setTimestamp()
                .setThumbnail('https://cdn.discordapp.com/emojis/742043142750388304.png');

            await interaction.update({ embeds: [updatedEmbed], components: [row] });

            // Send success message
            await interaction.followUp({ 
                content: '🎉 You have successfully entered the giveaway! Good luck!', 
                ephemeral: true 
            });
        });
    },
};